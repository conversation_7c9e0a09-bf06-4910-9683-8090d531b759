APP_NAME=MSSQL-Worker
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_TIMEZONE=UTC
APP_URL=http://mssql-worker.fdmc.com.tw

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=daily
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=sqlite
# DB_SQLITE_DATABASE=database/database.sqlite
# DB_SQLITE_FOREIGN_KEYS=true

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=database
CACHE_PREFIX=

# 以上為不需更動，以下為新增

# sqlsrv
DB_MSSQL_HOST=
DB_MSSQL_PORT=
DB_MSSQL_DATABASE=
DB_MSSQL_USERNAME=
DB_MSSQL_PASSWORD=
DB_MSSQL_CHARSET=
DB_MSSQL_COLLATION=
DB_MSSQL_ENCRYPT=
DB_MSSQL_TRUST_SERVER_CERTIFICATE=
# pgsql
DB_PGSQL_HOST=
DB_PGSQL_PORT=
DB_PGSQL_USERNAME=
DB_PGSQL_PASSWORD=
DB_PGSQL_CHARSET=

# pgsql.acc
DB_PGSQL_DEV_ACC_DATABASE=
DB_PGSQL_ACC_DATABASE=

# pgsql.hr
DB_PGSQL_HR_DATABASE=

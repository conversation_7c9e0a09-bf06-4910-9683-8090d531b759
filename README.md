# MSSQL Worker

MSSQL Worker 是一個基於 Laravel 框架的中介層服務，主要用於統一管理和處理 MSSQL 資料庫操作。該服務確保了 PHP 版本和 SQLSRV 驅動的相容性，並提供了一個穩定的介面來處理所有 MSSQL 相關的操作。

## 專案目的

- 統一管理 MSSQL 資料庫連接和操作
- 固定 PHP 版本和 SQLSRV 驅動版本，確保系統穩定性
- 提供標準化的 API 介面，簡化其他服務對 MSSQL 的訪問
- 作為中介層（Relay）服務，集中處理所有 MSSQL 相關操作

## 運行容器說明：
- 1) 請先安裝 docker 和 docker-compose
- 2) 使用 `make server` 即可。
- 3) 網頁訪問: `http://localhost:8888` 。
- 3-1) 運行 `make exec` 進入容器交互介面
- 3-2) 運行 `make srun` 執行 schedule run

## 系統需求

- PHP 版本：`8.2.25`
- Laravel 版本：`11.30.0`
- SQLSRV 驅動版本：`5.12.0`
- MSSQL Server：`Microsoft SQL Server 10.50.4000 | Microsoft SQL Server 2008 R2 (SP2) - 10.50.4000.0`

## 安全性考慮

- 所有請求都需要進行身份驗證
- 使用參數化查詢防止 SQL 注入
- 敏感資訊加密處理
- 請求限制和節流控制

## 監控和日誌

- 所有資料庫操作都會被記錄
- 使用 Laravel 的日誌系統進行追蹤
- 支援性能監控和統計

## 部署

> TODO

## 貢獻指南

> TODO

<?php

namespace App\Console\Commands\Acc;

use Illuminate\Console\Command;

use App\Models\Acc\PjPay;
use App\Models\Acc\PjPayDetail;
use App\Models\Acc\Venbud;
use Illuminate\Support\Facades\DB;

class BellErpGet extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app.acc:bellerp.get {symno} {batch} {epjno}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Bell ERP data by "symno, batch, epjno"';



    /**
     * Execute the console command.
     */
    public function handle()
    {
        \Log::channel('acc_cron')->info('Bell ERP Get start');

        $this->data();

        \Log::channel('acc_cron')->info('Bell ERP Get end');
    }

    public function data()
    {
        try {
            // PjPay
            $this->pjPay($this->argument('epjno'));
            \Log::channel('acc_cron')->info('pjpay end');

            // PjPayDetail
            // 目前 symno >'202201' 才有抓
            $this->pjPayDetail(
                $this->argument('symno'),
                $this->argument('batch'),
                $this->argument('epjno')
            );
            \Log::channel('acc_cron')->info('detail end');

            // v_epjvenbud
            $this->venbud($this->argument('epjno'));
            \Log::channel('acc_cron')->info('v_epjvenbud end');

        } catch (\Exception $e) {
            \Log::channel('acc_cron')->error($e);
        }
    }


    public function pjPay($epjno)
    {
        PjPay::where('epjno', $epjno)->delete();
        $query = DB::connection('sqlsrv')
            ->table('pjpay')
            ->selectRaw('*')
            // ->whereBetween('epjyn', ['20', '60'])
            ->where('epjno', $epjno);

        $pjpay = $query->cursor();
        $pjpay->chunk(700)->each(function ($chunked) {

            $chunk = [];
            // erp 的sql版本不支援 trim()
            $chunked->each(function ($item) use (&$chunk) {
                $payload = [
                    'epjacc' => trim($item->epjacc),
                    'epjno' => trim($item->epjno),
                    'payload' => json_encode([
                        'epjna' => trim($item->epjna),
                        'pdno' => trim($item->pdno ?? ''),
                        'pmno' => trim($item->pmno ?? ''),
                        'pcno' => trim($item->pcno ?? ''),
                        'pleper' => doubleval($item->pleper),
                        'venpayper' => doubleval($item->venpayper),
                        'difamt' => doubleval($item->difamt),
                        'sdate' => $item->sdate,
                        'odate' => $item->odate,
                        'atypena' => trim($item->atypena),
                        'qupamtc' => intval($item->qupamtc),
                        'qupamtad' => intval($item->qupamtad),
                        'qupamt' => intval($item->qupamt),
                        'tqurece' => intval($item->tqurece),
                        'tqurecn' => intval($item->tqurecn),
                        'budamt' => intval($item->budamt),
                        'vencamt' => intval($item->vencamt),
                        'tvpay' => intval($item->tvpay),
                        'nacpay' => intval($item->nacpay),
                    ])
                ];

                array_push($chunk, (array) $payload);
            });

            DB::connection('pgsql.acc')->table('pj_pays')->insert($chunk);
        });
    }

    public function pjPayDetail($symno, $pbatchno, $epjno)
    {
        $connectionPostgreSQL = DB::connection('pgsql.acc');
        PjPayDetail::where('symno', $symno)
            ->where('pbatchno', $pbatchno)
            ->where('epjno', $epjno)
            ->delete();
        $query = DB::connection('sqlsrv')
            ->table('pjpay_detail')
            ->selectRaw('*')
            ->where('symno', $symno)
            ->where('pbatchno', $pbatchno)
            ->where('epjno', $epjno);

        $batch = [];
        $batchSize = 400;

        foreach ($query->cursor() as $item) {
            $batch[] = [
                'symno' => $item->symno,
                'pbatchno' => trim($item->pbatchno),
                'pbatchna' => trim($item->pbatchna),
                'epjacc' => trim($item->epjacc),
                'epjno' => trim($item->epjno),
                'unkey' => intval($item->unkey),
                'payload' => json_encode([
                    'venno' => trim($item->venno),
                    'venna' => $item->venna,
                    'copname' => trim($item->copname),
                    'payrmk' => $item->payrmk,
                    'vencamt' => intval($item->vencamt),
                    'tvenpay' => intval($item->tvenpay),
                    'balance' => intval($item->balance),
                    'npay' => intval($item->npay),
                    'ppay' => intval($item->ppay),
                    'hpay' => intval($item->hpay),
                    'dpay' => intval($item->dpay),
                    'venpay' => intval($item->venpay),
                    'tax' => 0,
                    'pjctrno' => trim($item->pjctrno),
                    'po' => trim($item->po),
                    '_unkey' => intval($item->_unkey),
                ])
            ];

            //  $batch[] = $payload;

            // 當批次達到指定大小時寫入資料庫
            if (count($batch) >= $batchSize) {
                $connectionPostgreSQL->table('pj_pay_details')->insert($batch);
                $batch = [];
                gc_collect_cycles();
            }
        }

        // 處理最後剩餘的資料
        if (!empty($batch)) {
            $connectionPostgreSQL->table('pj_pay_details')->insert($batch);
        }

        $batch = [];
        unset($query);
        gc_collect_cycles();
    }

    public function venbud($epjno)
    {
        $chunkSize = 1000; // 每页数据大小
        $page = 1; // 起始页
        $connectionMSSQL = DB::connection('sqlsrv');
        $connectionPostgreSQL = DB::connection('pgsql.acc');

        Venbud::where('epjno', $epjno)
            ->delete();
        while (true) {
            $offset = ($page - 1) * $chunkSize + 1;
            $end = $offset + $chunkSize - 1;

            // 使用 ROW_NUMBER() 实现分页
            $query = $connectionMSSQL->select(
                "WITH FilteredData AS (
                        SELECT DISTINCT epjno
                        FROM pjpay_detail
                        WHERE epjno = ?
                    ),
                    PaginatedData AS (
                        SELECT 
                            RTRIM(v.epjno) AS epjno,
                            RTRIM(v.budcno) AS budcno,
                            v.budcna,
                            CAST(v.quamt AS INT) AS quamt,
                            CAST(v.qudcp AS INT) AS qudcp,
                            CAST(v.budamt AS INT) AS budamt,
                            CAST(v.buddcp AS INT) AS buddcp,
                            RTRIM(v.venno) AS venno,
                            RTRIM(v.venna) AS venna,
                            CAST(v.vencamt AS INT) AS vencamt,
                            CAST(v.vendcp AS INT) AS vendcp,
                            CAST(v.tvencamt AS INT) AS tvencamt,
                            CAST(v.venpay AS INT) AS venpay,
                            v.rmk,
                            CAST(v.budkey AS INT) AS budkey,
                            v.pjbno,
                            v.pjbud,
                            CAST(v.vencamt2 AS INT) AS vencamt2,
                            CAST(v.vendcp2 AS INT) AS vendcp2,
                            CAST(v.tvencamt2 AS INT) AS tvencamt2,
                            CAST(v.venpay2 AS INT) AS venpay2,
                            CAST(v.quamt2 AS INT) AS quamt2,
                            CAST(v.qudcp2 AS INT) AS qudcp2,
                            CAST(v.budamt2 AS INT) AS budamt2,
                            CAST(v.buddcp2 AS INT) AS buddcp2,
                            v.curno,
                            RTRIM(v.sdate) AS sdate,
                            RTRIM(v.odate) AS odate,
                            RTRIM(v.cdate) AS cdate,
                            CAST(v.budnew AS INT) AS budnew,
                            CAST(v.budnew2 AS INT) AS budnew2,
                            CAST(v.vendp1 AS INT) AS vendp1,
                            CAST(v.dpay AS INT) AS dpay,
                            ROW_NUMBER() OVER (ORDER BY v.epjno) AS RowNum
                        FROM v_epjvenbud v
                        WHERE v.epjno IN (SELECT epjno FROM FilteredData)
                    )
                    SELECT *
                    FROM PaginatedData
                    WHERE RowNum BETWEEN ? AND ?",
                [$epjno, $offset, $end]
            );


            $chunk = array_map(function ($item) {
                unset($item->RowNum);
                return (array) $item;
            }, $query);

            foreach (array_chunk($chunk, 200) as $batch) {
                $connectionPostgreSQL->table('venbuds')->insert($batch);
                unset($batch);
            }

            // 如果当前结果少于分页大小，说明数据读取完成
            if (count($chunk) < $chunkSize) {
                break;
            }

            $page++;
            unset($query); // 释放查询结果
            unset($chunk); // 释放查询结果
        }
    }
}

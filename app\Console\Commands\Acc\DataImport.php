<?php

namespace App\Console\Commands\Acc;

use App\Jobs\PjPayDetailImportJob;
use App\Models\Acc\Email;
use Illuminate\Console\Command;
use App\Models\Acc\PjPay;
use Illuminate\Support\Facades\DB;

class DataImport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app.acc:data.import';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        \Log::channel('acc_cron')->info('ERP import to acc start');

        $this->data();
        \Log::channel('acc_cron')->info('import to acc end');

    }

    public function data()
    {
        try {
            // PjPay
            $this->pjPay();

            PjPayDetailImportJob::dispatch();

            // email
            $this->email();
        } catch (\Exception $e) {
            \Log::channel('acc_cron')->error($e);
        }

    }

    public function pjPay()
    {
        $query = DB::connection('sqlsrv')
            ->table('pjpay')
            ->selectRaw('*');
        // ->whereBetween('epjyn', ['20', '60']);

        PjPay::truncate();
        PjPay::on('pgsql.dev.acc')->truncate();
        $pjpay = $query->cursor();
        $pjpay->chunk(700)->each(function ($chunked) {

            $chunk = [];
            // erp 的sql版本不支援 trim()
            $chunked->each(function ($item) use (&$chunk) {
                $payload = [
                    'epjacc' => trim($item->epjacc),
                    'epjno' => trim($item->epjno),
                    'payload' => json_encode([
                        'epjna' => trim($item->epjna),
                        'pdno' => trim($item->pdno ?? ''),
                        'pmno' => trim($item->pmno ?? ''),
                        'pcno' => trim($item->pcno ?? ''),
                        'pleper' => doubleval($item->pleper),
                        'venpayper' => doubleval($item->venpayper),
                        'difamt' => doubleval($item->difamt),
                        'sdate' => $item->sdate,
                        'odate' => $item->odate,
                        'atypena' => trim($item->atypena),
                        'qupamtc' => intval($item->qupamtc),
                        'qupamtad' => intval($item->qupamtad),
                        'qupamt' => intval($item->qupamt),
                        'tqurece' => intval($item->tqurece),
                        'tqurecn' => intval($item->tqurecn),
                        'budamt' => intval($item->budamt),
                        'vencamt' => intval($item->vencamt),
                        'tvpay' => intval($item->tvpay),
                        'nacpay' => intval($item->nacpay),
                    ])
                ];

                array_push($chunk, (array) $payload);
            });

            DB::connection('pgsql.acc')->table('pj_pays')->insert($chunk);
            DB::connection('pgsql.dev.acc')->table('pj_pays')->insert($chunk);
            unset($chunk, $chunked, $payload);
            gc_collect_cycles();
        });
        unset($pjpay);
    }
    public function email()
    {
        $mail_query = DB::connection('sqlsrv')
            ->table('email')
            ->selectRaw('*')
            // ->get();
        ;

        Email::truncate();
        Email::on('pgsql.dev.acc')->truncate();

        $mail = $mail_query->cursor();
        $mail->chunk(700)->each(function ($chunked) {
            $chunk = [];

            $chunked->each(function ($item) use (&$chunk) {
                $payload = [
                    'venno' => trim($item->venno),
                    'venna' => trim($item->venna),
                    'email' => trim($item->email),
                    'faxno' => trim($item->faxno),
                ];

                array_push($chunk, (array) $payload);
            });

            DB::connection('pgsql.acc')->table('emails')->insert($chunk);
            DB::connection('pgsql.dev.acc')->table('emails')->insert($chunk);
            unset($chunk);
        });
        unset($email);
    }

}

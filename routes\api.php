<?php

// use App\Http\Controllers\Acc\PjPayController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Carbon\Carbon;


Route::post('rh-erp-auth', function () {
    \FDMC\RH\ERP\Auth\Server::handle(request()->all());
});
Route::get('test', function () {
    DB::table('jobs')->get();
});

route::prefix('acc')->group(function () {
    // 即時撈取專案
    Route::post('/fetch/erp', function (Request $request) {

        $symno = $request->input('symno');
        $batch = $request->input('batch');
        $epjno = $request->input('erpEpjno');

        if (!isset($symno) || empty($symno)) {
            return response('', 400);
        }
        $date = Carbon::parse($symno)->setTimezone('Asia/Taipei');

        \Artisan::queue('app.acc:bellerp.get', [
            'symno' => $date->format('Ym'),
            'batch' => $batch,
            'epjno' => $epjno,
        ]);

        return response()->json(['status' => 'success']);
    });

    // 從正式區dump data到測試區
    Route::post('/dev/dump/sql', function (Request $request) {
        // DB::connection('pgsql.dev.acc')->select($request->input('sql'));

        # 從Beta dump 出來
        // pg_dump -h *********** -U postgres -W -Fc -b -v -f ACC_DEV-2024-12-27.dump FDMC_ACC_DEV

        #倒回去Gamma
        // pg_restore -h *********** -p 5432 -U postgres -d FDMC_ACC_DEV -v ACC_DEV-2024-12-27.dump

        // Process::run();

    });

});

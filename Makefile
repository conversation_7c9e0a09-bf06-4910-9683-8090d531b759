.PHONY: build exec serve ensure-running slist srun queue-work ti

build:
	@echo "Building..."
	@docker build --platform=linux/amd64 -t mssql-worker .

ensure-running:
	@if [ "$$(docker ps -q -f name=mssql-worker)" = "" ]; then \
		echo "Starting container..."; \
		$(MAKE) serve; \
		sleep 5; \
	fi

exec: ensure-running
	@echo "Running..."
	@docker exec -it mssql-worker bash

serve:
	@echo "Running..."
	@docker run --rm -d -v ${PWD}:/var/www/html -p 8888:80 --name mssql-worker mssql-worker

slist: ensure-running
	@docker exec mssql-worker php artisan schedule:list

srun: ensure-running
	@docker exec mssql-worker php artisan schedule:run

queue-work: ensure-running
	@docker exec -t mssql-worker php artisan queue:work

ti:
	@docker exec -it mssql-worker php artisan tinker

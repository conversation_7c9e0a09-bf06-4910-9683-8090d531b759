<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote')->hourly();

Schedule::command('app.hr:fetch.project')
    ->timezone('Asia/Taipei')
    ->at('08:00')
    ->withoutOverlapping(); // 避免重複執行

// 撈取專案付款資料到acc
Schedule::command('app.acc:data.import')
    ->dailyAt('16:00');
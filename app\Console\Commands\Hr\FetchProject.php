<?php

namespace App\Console\Commands\Hr;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Throwable;

class FetchProject extends Command
{
    const CHANNEL_NAME = 'hr_cron';

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app.hr:fetch.project';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetch project data from ERP system.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            Log::channel(self::CHANNEL_NAME)->info('[Fetching Project] Start transform from ERP to HR system.');

            $query = DB::connection('sqlsrv')
                ->table('wap_bepj')
                ->selectRaw('LTRIM(RTRIM(epjno)) as epjno, LTRIM(RTRIM(accasno)) as accasno, LTRIM(RTRIM(epjna)) as epjna')
                ->whereRaw('odate=null')
                ->orWhereRaw("odate='無完工日'")
                ->orWhereRaw('ISDATE(odate) = 1 AND CAST(odate as DATE) >= DATEADD(MONTH, -36, GETDATE())');

            $count = $query->count();
            Log::channel(self::CHANNEL_NAME)->info('[Fetching Project] count: ' . $count);

            if($count > 0) {
                DB::connection('pgsql.hr')->statement('TRUNCATE project_data RESTART IDENTITY CASCADE;');

                $data = $query->cursor();
                $data->chunk(700)->each(function($chunked) {
                    $chunk = [];
                    $chunked->each(function($item) use (&$chunk) {
                        $payload = [
                            'payload' => json_encode([
                                'project_no' => $item->epjno,
                                'project_name' => $item->epjna,
                                'accounting_no' => $item->accasno
                            ]),
                            'created_at' => now()->format('Y-m-d H:i:s'),
                        ];
                        $chunk[] = (array)$payload;
                    });
                    DB::connection('pgsql.hr')->table('project_data')->insert($chunk);
                });
            }
            Log::channel(self::CHANNEL_NAME)->info('[Fetching Project] completed !');
        } catch (Throwable $th) {
            Log::channel(self::CHANNEL_NAME)->info('[Fetching Project] Error: ', [
                'from' => __CLASS__,
                'error' => $th->getMessage(),
            ]);
        }
    }
}

#!/bin/bash
# https://learn.microsoft.com/zh-tw/sql/connect/odbc/linux-mac/installing-the-microsoft-odbc-driver-for-sql-server

# Define colors
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Get Debian version
DEBIAN_VERSION=$(lsb_release -rs)

# Environment variables with default values
INSTALL_UNIXODBC=${INSTALL_UNIXODBC:-"true"}
INSTALL_GSSAPI=${INSTALL_GSSAPI:-"true"}

# Print installation configuration
print_config() {
    echo -e "\n${YELLOW}[CONFIG] Installation Configuration:${NC}"
    echo -e "${BLUE}Debian Version:${NC} ${DEBIAN_VERSION}"
    echo -e "${BLUE}Install unixodbc-dev:${NC} ${INSTALL_UNIXODBC}"
    echo -e "${BLUE}Install libgssapi-krb5-2:${NC} ${INSTALL_GSSAPI}"
    echo -e "${BLUE}Required Packages:${NC}"
    echo -e "  - msodbcsql18"
    echo -e "  - mssql-tools18"
    [ "$INSTALL_UNIXODBC" = "true" ] && echo -e "  - unixodbc-dev"
    [ "$INSTALL_GSSAPI" = "true" ] && echo -e "  - libgssapi-krb5-2"
    echo -e "${BLUE}Repository URL:${NC} https://packages.microsoft.com/config/debian/${DEBIAN_VERSION}/prod.list"
    echo -e "${BLUE}Installation Path:${NC} /opt/mssql-tools18/bin\n"
}

# Function to install Microsoft repository keys based on Debian version
install_microsoft_keys() {
    # Convert version to integer for comparison (e.g., 12.0 -> 12)
    VERSION_NUM=${DEBIAN_VERSION%.*}
    if [ "$VERSION_NUM" -ge 12 ]; then
        echo -e "${BLUE}[DEBUG] Using GPG method for Debian 12+${NC}"
        curl -fsSL https://packages.microsoft.com/keys/microsoft.asc | gpg --dearmor -o /usr/share/keyrings/microsoft-prod.gpg
    else
        echo -e "${BLUE}[DEBUG] Using legacy method for Debian <12${NC}"
        curl https://packages.microsoft.com/keys/microsoft.asc > /etc/apt/trusted.gpg.d/microsoft.asc
    fi
}

# Function to add Microsoft repository
add_microsoft_repository() {
    curl "https://packages.microsoft.com/config/debian/${DEBIAN_VERSION}/prod.list" > /etc/apt/sources.list.d/mssql-release.list
}

# Main installation function
install_mssql_tools() {
    # Build package list
    local PACKAGES="msodbcsql18 mssql-tools18"
    [ "$INSTALL_UNIXODBC" = "true" ] && PACKAGES="$PACKAGES unixodbc-dev"
    [ "$INSTALL_GSSAPI" = "true" ] && PACKAGES="$PACKAGES libgssapi-krb5-2"

    echo -e "${BLUE}[DEBUG] Installing packages:${NC} ${YELLOW}${PACKAGES}${NC}"

    # Update package list
    apt-get update

    # Install packages
    ACCEPT_EULA=Y apt-get install -y $PACKAGES

    # Add SQL Tools to PATH in global profile
    echo 'export PATH="$PATH:/opt/mssql-tools18/bin"' >> /etc/profile

    # Clean up
    rm -rf /var/lib/apt/lists/*
}

# Main execution
echo -e "${BLUE}[INFO] Detecting Debian version:${NC} ${YELLOW}${DEBIAN_VERSION}${NC}"

# Print configuration
print_config

# Install repository keys
echo -e "\n${BLUE}[INFO] Installing Microsoft repository keys...${NC}"
install_microsoft_keys

# Add Microsoft repository
echo -e "\n${BLUE}[INFO] Adding Microsoft repository...${NC}"
add_microsoft_repository

# Install MSSQL tools and dependencies
echo -e "\n${BLUE}[INFO] Installing MSSQL tools and dependencies...${NC}"
install_mssql_tools

echo -e "\n${GREEN}[SUCCESS] Installation completed successfully!${NC}"

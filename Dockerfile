FROM php:8.2.25-cli

# Install dependencies
RUN apt update && apt-get install -y \
    gnupg2 \
    curl \
    unixodbc \
    unixodbc-dev \
    libpq-dev \
    git \
    unzip \
    vim \
    lsb-release \
    && rm -rf /var/lib/apt/lists/*

# Add Microsoft repo for MSSQL ODBC Driver
COPY ./deployment/install_odbc18_debian.sh /install_odbc18_debian.sh
RUN INSTALL_UNIXODBC=false INSTALL_GSSAPI=false bash /install_odbc18_debian.sh

# composer install
RUN php -r "copy('https://getcomposer.org/installer', 'composer-setup.php');" \
    && php -r "if (hash_file('sha384', 'composer-setup.php') === 'dac665fdc30fdd8ec78b38b9800061b4150413ff2e3b6f88543c636f7cd84f6db9189d43a81e5503cda447da73c7e5b6') { echo 'Installer verified'; } else { echo 'Installer corrupt'; unlink('composer-setup.php'); } echo PHP_EOL;" \
    && php composer-setup.php \
    && php -r "unlink('composer-setup.php');" \
    && chmod +x composer.phar \
    && mv composer.phar /usr/local/bin/composer \
    && docker-php-ext-install pdo \
    && pecl install https://pecl.php.net/get/sqlsrv-5.12.0.tgz https://pecl.php.net/get/pdo_sqlsrv-5.12.0.tgz \
    && docker-php-ext-enable sqlsrv pdo_sqlsrv \
    && docker-php-ext-install pgsql pdo_pgsql

COPY deployment/openssl.cnf /etc/ssl/openssl.cnf
ENV OPENSSL_CONF=/etc/ssl/openssl.cnf

# Set working directory
WORKDIR /var/www/html

EXPOSE 80

CMD ["php", "artisan", "serve", "--host=0.0.0.0", "--port=80"]

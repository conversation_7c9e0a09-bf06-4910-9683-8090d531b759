<?php

use Illuminate\Support\Str;

return [

    /*
    |--------------------------------------------------------------------------
    | Default Database Connection Name
    |--------------------------------------------------------------------------
    |
    | Here you may specify which of the database connections below you wish
    | to use as your default connection for database operations. This is
    | the connection which will be utilized unless another connection
    | is explicitly specified when you execute a query / statement.
    |
    */

    'default' => env('DB_CONNECTION', 'sqlite'),

    /*
    |--------------------------------------------------------------------------
    | Database Connections
    |--------------------------------------------------------------------------
    |
    | Below are all of the database connections defined for your application.
    | An example configuration is provided for each database system which
    | is supported by Laravel. You're free to add / remove connections.
    |
    */

    'connections' => [
        'sqlite' => [
            'driver' => 'sqlite',
            'database' => env('DB_SQLITE_DATABASE', database_path('database.sqlite')),
            'timeout' => 60, // 從預設值增加（通常是 5-10 秒）
            'options' => [
                PDO::ATTR_TIMEOUT => 60,
            ],
            'prefix' => '',
            'foreign_key_constraints' => env('DB_SQLITE_FOREIGN_KEYS', true),
        ],

        'sqlsrv' => [
            'driver' => 'sqlsrv',
            'host' => env('DB_MSSQL_HOST', 'localhost'),
            'port' => env('DB_MSSQL_PORT', '1433'),
            'database' => env('DB_MSSQL_DATABASE', 'funcsync'),
            'username' => env('DB_MSSQL_USERNAME', 'root'),
            'password' => env('DB_MSSQL_PASSWORD', ''),
            'charset' => env('DB_MSSQL_CHARSET', 'cp950'),
            'collation' => env('DB_MSSQL_COLLATION', 'Chinese_Taiwan_Stroke_CI_AS'),
            'prefix' => '',
            'prefix_indexes' => true,
            'encrypt' => env('DB_MSSQL_ENCRYPT', 'yes'),
            'trust_server_certificate' => env('DB_MSSQL_TRUST_SERVER_CERTIFICATE', 'true'),
        ],

        'pgsql.dev.acc' => [
            'driver' => 'pgsql',
            'host' => env('DB_PGSQL_DEV_HOST', '***********'),
            'port' => env('DB_PGSQL_PORT', '5432'),
            'username' => env('DB_PGSQL_USERNAME', 'postgres'),
            'password' => env('DB_PGSQL_PASSWORD', 'postgres'),
            'charset' => env('DB_PGSQL_DEV_CHARSET', 'utf8'),

            'database' => env('DB_PGSQL_DEV_ACC_DATABASE', 'FDMC_ACC_DEV'),

            'prefix' => '',
            'prefix_indexes' => true,
            'search_path' => 'public',
            'sslmode' => 'prefer',
        ],

        'pgsql.acc' => [
            'driver' => 'pgsql',
            'host' => env('DB_PGSQL_HOST', '127.0.0.1'),
            'port' => env('DB_PGSQL_PORT', '5432'),
            'username' => env('DB_PGSQL_USERNAME', 'postgres'),
            'password' => env('DB_PGSQL_PASSWORD', 'postgres'),
            'charset' => env('DB_PGSQL_CHARSET', 'utf8'),

            'database' => env('DB_PGSQL_ACC_DATABASE', 'FDMC_ACC'),

            'prefix' => '',
            'prefix_indexes' => true,
            'search_path' => 'public',
            'sslmode' => 'prefer',
        ],

        'pgsql.hr' => [
            'driver' => 'pgsql',
            'host' => env('DB_PGSQL_HOST', '127.0.0.1'),
            'port' => env('DB_PGSQL_PORT', '5432'),
            'username' => env('DB_PGSQL_USERNAME', 'postgres'),
            'password' => env('DB_PGSQL_PASSWORD', 'postgres'),
            'charset' => env('DB_PGSQL_CHARSET', 'utf8'),

            'database' => env('DB_PGSQL_HR_DATABASE', 'FDMC_HR_DEV'),

            'prefix' => '',
            'prefix_indexes' => true,
            'search_path' => 'public',
            'sslmode' => 'prefer',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Migration Repository Table
    |--------------------------------------------------------------------------
    |
    | This table keeps track of all the migrations that have already run for
    | your application. Using this information, we can determine which of
    | the migrations on disk haven't actually been run on the database.
    |
    */

    'migrations' => [
        'table' => 'migrations',
        'update_date_on_publish' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Redis Databases
    |--------------------------------------------------------------------------
    |
    | Redis is an open source, fast, and advanced key-value store that also
    | provides a richer body of commands than a typical key-value system
    | such as Memcached. You may define your connection settings here.
    |
    */

    'redis' => [

        'client' => env('REDIS_CLIENT', 'phpredis'),

        'options' => [
            'cluster' => env('REDIS_CLUSTER', 'redis'),
            'prefix' => env('REDIS_PREFIX', Str::slug(env('APP_NAME', 'laravel'), '_').'_database_'),
        ],

        'default' => [
            'url' => env('REDIS_URL'),
            'host' => env('REDIS_HOST', '127.0.0.1'),
            'username' => env('REDIS_USERNAME'),
            'password' => env('REDIS_PASSWORD'),
            'port' => env('REDIS_PORT', '6379'),
            'database' => env('REDIS_DB', '0'),
        ],

        'cache' => [
            'url' => env('REDIS_URL'),
            'host' => env('REDIS_HOST', '127.0.0.1'),
            'username' => env('REDIS_USERNAME'),
            'password' => env('REDIS_PASSWORD'),
            'port' => env('REDIS_PORT', '6379'),
            'database' => env('REDIS_CACHE_DB', '1'),
        ],

    ],

];

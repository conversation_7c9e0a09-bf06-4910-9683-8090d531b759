<?php

namespace App\Jobs;

use App\Models\Acc\PjPayDetail;
use App\Models\Acc\Venbud;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use DB;

class PjPayDetailImportJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        \Log::channel('acc_cron')->info('detail start');
        // 目前 symno >'202201' 才有抓
        $this->pjPayDetail();
        $this->venbud();
        \Log::channel('acc_cron')->info('detail end');
    }

    public function pjPayDetail()
    {
        try {
            $query = DB::connection('sqlsrv')
                ->table('pjpay_detail')
                ->selectRaw('*')
                // ->get();
            ;
            PjPayDetail::truncate();
            PjPayDetail::on('pgsql.dev.acc')->truncate();

            $connectionPostgreSQL = DB::connection('pgsql.acc');
            $connectionDev = DB::connection('pgsql.dev.acc');

            // 使用 Generator 處理資料
            $batch = [];
            $batchSize = 400;

            foreach ($query->cursor() as $item) {
                $batch[] = [
                    'symno' => $item->symno,
                    'pbatchno' => trim($item->pbatchno),
                    'pbatchna' => trim($item->pbatchna),
                    'epjacc' => trim($item->epjacc),
                    'epjno' => trim($item->epjno),
                    'unkey' => intval($item->unkey),
                    'payload' => json_encode([
                        'venno' => trim($item->venno),
                        'venna' => $item->venna,
                        'copname' => trim($item->copname),
                        'payrmk' => $item->payrmk,
                        'vencamt' => intval($item->vencamt),
                        'tvenpay' => intval($item->tvenpay),
                        'balance' => intval($item->balance),
                        'npay' => intval($item->npay),
                        'ppay' => intval($item->ppay),
                        'hpay' => intval($item->hpay),
                        'dpay' => intval($item->dpay),
                        'venpay' => intval($item->venpay),
                        'tax' => 0,
                        'pjctrno' => trim($item->pjctrno),
                        'po' => trim($item->po),
                        '_unkey' => intval($item->_unkey),
                    ])
                ];

                //  $batch[] = $payload;

                // 當批次達到指定大小時寫入資料庫
                if (count($batch) >= $batchSize) {
                    $connectionPostgreSQL->table('pj_pay_details')->insert($batch);
                    $connectionDev->table('pj_pay_details')->insert($batch);
                    $batch = [];
                    gc_collect_cycles();
                }
            }

            // 處理最後剩餘的資料
            if (!empty($batch)) {
                $connectionPostgreSQL->table('pj_pay_details')->insert($batch);
                $connectionDev->table('pj_pay_details')->insert($batch);
            }

            $batch = [];
            unset($query);
            gc_collect_cycles();
        } catch (\Throwable $th) {
            \Log::error($th);
        }
    }

    public function venbud()
    {
        try {
            $chunkSize = 1000; // 每页数据大小
            $page = 1; // 起始页
            $connectionMSSQL = DB::connection('sqlsrv');
            $connectionPostgreSQL = DB::connection('pgsql.acc');
            $connectionDev = DB::connection('pgsql.dev.acc');

            Venbud::truncate();
            Venbud::on('pgsql.dev.acc')->truncate();
            while (true) {
                $offset = ($page - 1) * $chunkSize + 1;
                $end = $offset + $chunkSize - 1;

                // 使用 ROW_NUMBER() 实现分页
                $query = $connectionMSSQL->select(
                    "WITH FilteredData AS (
                        SELECT DISTINCT epjno
                        FROM pjpay_detail
                    ),
                    PaginatedData AS (
                        SELECT
                            RTRIM(v.epjno) AS epjno,
                            RTRIM(v.budcno) AS budcno,
                            v.budcna,
                            CAST(v.quamt AS INT) AS quamt,
                            CAST(v.qudcp AS INT) AS qudcp,
                            CAST(v.budamt AS INT) AS budamt,
                            CAST(v.buddcp AS INT) AS buddcp,
                            RTRIM(v.venno) AS venno,
                            RTRIM(v.venna) AS venna,
                            CAST(v.vencamt AS INT) AS vencamt,
                            CAST(v.vendcp AS INT) AS vendcp,
                            CAST(v.tvencamt AS INT) AS tvencamt,
                            CAST(v.venpay AS INT) AS venpay,
                            v.rmk,
                            CAST(v.budkey AS INT) AS budkey,
                            v.pjbno,
                            v.pjbud,
                            CAST(v.vencamt2 AS INT) AS vencamt2,
                            CAST(v.vendcp2 AS INT) AS vendcp2,
                            CAST(v.tvencamt2 AS INT) AS tvencamt2,
                            CAST(v.venpay2 AS INT) AS venpay2,
                            CAST(v.quamt2 AS INT) AS quamt2,
                            CAST(v.qudcp2 AS INT) AS qudcp2,
                            CAST(v.budamt2 AS INT) AS budamt2,
                            CAST(v.buddcp2 AS INT) AS buddcp2,
                            v.curno,
                            RTRIM(v.sdate) AS sdate,
                            RTRIM(v.odate) AS odate,
                            RTRIM(v.cdate) AS cdate,
                            CAST(v.budnew AS INT) AS budnew,
                            CAST(v.budnew2 AS INT) AS budnew2,
                            CAST(v.vendp1 AS INT) AS vendp1,
                            CAST(v.dpay AS INT) AS dpay,
                            ROW_NUMBER() OVER (ORDER BY v.epjno) AS RowNum
                        FROM v_epjvenbud v
                        WHERE v.epjno IN (SELECT epjno FROM FilteredData)
                    )
                    SELECT *
                    FROM PaginatedData
                    WHERE RowNum BETWEEN ? AND ?",
                    [$offset, $end]
                );


                $chunk = array_map(function ($item) {
                    unset($item->RowNum);
                    return (array) $item;
                }, $query);

                foreach (array_chunk($chunk, 200) as $batch) {
                    $connectionPostgreSQL->table('venbuds')->insert($batch);
                    $connectionDev->table('venbuds')->insert($batch);
                    unset($batch);
                }

                // 如果当前结果少于分页大小，说明数据读取完成
                if (count($chunk) < $chunkSize) {
                    break;
                }

                $page++;
                unset($query); // 释放查询结果
                unset($chunk); // 释放查询结果
            }
        } catch (\Throwable $th) {
            \Log::channel('acc_cron')->error("Error on page: {$page}");
            \Log::channel('acc_cron')->error($th);
        }

    }
}

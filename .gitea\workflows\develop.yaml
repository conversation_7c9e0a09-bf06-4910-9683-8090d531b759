name: Deploy
run-name: ${{ github.actor }} - ${{ gitea.ref_name }} is development out GitHub Actions 🚀

on:
  push:
    branches:
      - dev-master
  pull_request:
    branches:
      - dev-master

jobs:
  build:
    runs-on: gamma
    env:
      DEPLOY_PATH: ${{ vars.DEV_DEPLOY_PATH }}
    steps:
      - uses: actions/checkout@v4

      - name: 系統資訊與資源庫檔案
        run: |
          echo "User: $(whoami)"
          echo "Shell: $SHELL"
          echo "Home directory: $HOME"
          id
          env
          ls ${{ github.workspace }}
